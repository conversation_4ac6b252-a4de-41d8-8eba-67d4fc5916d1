<!--@subject New Corporate Account Request Submitted @-->
<!--@vars {
"var customer_name":"Customer Name",
"var customer_email":"Customer Email",
"var company_name":"Company Name",
"var company_type":"Company Type",
"var business_email":"Business Email",
"var business_phone":"Business Phone",
"var request_id":"Request ID",
"var admin_url":"Admin URL",
"var store.frontend_name":"Store Name",
"var submission_date":"Submission Date"
} @-->

{{template config_path="design/email/header_template"}}

<table>
    <tr class="email-intro">
        <td>
            <p class="greeting">{{trans "Dear Admin,"}}</p>
            <p>
                {{trans "A new corporate account request has been submitted and requires your review."}}
            </p>
        </td>
    </tr>
    <tr class="email-summary">
        <td>
            <h3>{{trans "Request Details:"}}</h3>
            <table class="order-details">
                <tr>
                    <td><strong>{{trans "Request ID:"}}</strong></td>
                    <td>{{var request_id}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Customer Name:"}}</strong></td>
                    <td>{{var customer_name}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Customer Email:"}}</strong></td>
                    <td>{{var customer_email}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Company Name:"}}</strong></td>
                    <td>{{var company_name}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Company Type:"}}</strong></td>
                    <td>{{var company_type}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Business Email:"}}</strong></td>
                    <td>{{var business_email}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Business Phone:"}}</strong></td>
                    <td>{{var business_phone}}</td>
                </tr>
                <tr>
                    <td><strong>{{trans "Submitted Date:"}}</strong></td>
                    <td>{{var submission_date}}</td>
                </tr>
            </table>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            <p>
                {{trans "Please review this request and take appropriate action (approve or decline) as soon as possible."}}
            </p>
            <p>
                {{trans "To review this request:"}}
            </p>
            <ol>
                <li>{{trans "Log in to the admin panel"}}</li>
                <li>{{trans "Navigate to Customers > Corporate Account Requests"}}</li>
                <li>{{trans "Find request ID %request_id and click to review" request_id=$request_id}}</li>
                <li>{{trans "Approve or decline the request with appropriate notes"}}</li>
            </ol>
            <p>
                {{trans "The customer will be automatically notified via email once you process their request."}}
            </p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer_template"}}
