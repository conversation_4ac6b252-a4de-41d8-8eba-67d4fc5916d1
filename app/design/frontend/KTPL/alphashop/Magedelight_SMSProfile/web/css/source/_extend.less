/**
 * Theme extension for Magedelight_SMSProfile styles
 */

// Extend the module styles here
// This file will be loaded after the original module styles

& when (@media-common =true) {

    .customer-forgotpassword-page-wrapper,
    .customer-create-page-wrapper {
        .fieldset>.field:not(.choice)>.control {
            width: 100%;
        }

        fieldset.create.account {
            margin-bottom: 0;

           
        }

        fieldset.additional_info {
            .tooltip.wrapper .tooltip.toggle {
                font-weight: normal;
                text-decoration: underline;
                margin-left: 5px;
            }
        }
    }

    .validate-intl-phone{
        padding-left: 80px !important;
    }

    .customer-account-forgotpassword {
        .columns {
            .field.mobile{
                padding-right: 0;
            }
        }
        .control.captcha-image {
            .lib-vendor-prefix-display();
            .lib-vendor-box-align();
            flex-direction: column;
            margin-top: 20px;
            gap: 10px;

            .captcha-img {
                margin: 0;
            }
        }
    }

    .customer-create-form-container {
        .lib-vendor-prefix-display();

        .block-login-link {
            .block-title {
                font-size: 22px;
                margin-bottom: 20px;
            }

            .block-content {
                max-width: 300px;

                p {
                    .lib-font-size(16);
                }
            }

            .actions-toolbar {
                margin-top: 40px;

                .primary {
                    max-width: 200px;
                    width: 100%;
                }


                .action.primary {
                    width: 100%;
                }
            }
        }
    }

    .login-container .form-field__control.control.mobile {
        padding-right: 0;
    }

    // Common styles for all screen sizes
    .login-container,.customer-contact-page-wrapper,
    .customer-forgotpassword-page-wrapper,
    .customer-createpassword-page-wrapper,
    .customer-create-page-wrapper {
        flex-wrap: nowrap;

        .fieldset {
            &:after {
                content: '';
                margin: 0;
            }

            &:last-child {
                margin: 0;
            }
        }

        .mdsms {
            .mdsms__button-container {
                .btn.active {
                    .mdsms__button {
                        background: transparent;
                        border: none;
                        border-bottom: 3px solid;
                        box-shadow: none;
                        color: black;
                    }
                }

                .btn-password,
                .btn-otp {
                    flex-basis: auto;

                    .mdsms__button {
                        .lib-font-size(14);
                        color: #666666;
                        background: transparent;
                        box-shadow: none;
                        .lib-css(font-weight, @font-weight__regular);
                        border-bottom: 3px solid transparent;
                    }
                }
            }
        }

        .mdsms-login-form-wrapper {
            .lib-vendor-prefix-flex-basis(100%);
        }

        .login-left,.form.contact, 
        .forgot-parent,
        .form-create-account {
            background-color: @gray-100;
            padding: 15px;
            flex-basis: 100%;
            .lib-clearfix();

            select,
            input,
            .iti--allow-dropdown input,
            .iti--allow-dropdown input[type=text],
            .iti--allow-dropdown input[type=tel],
            .iti--separate-dial-code input,
            .iti--separate-dial-code input[type=text],
            .iti--separate-dial-code input[type=tel] {
                .lib-form-element-input(@_type: input-text,
                    @_color: @gray-600,
                    @_border: 1px solid #CCCCCC,
                    @_border-radius: 3px,
                    @_width: 100%,
                    @_height: 50px,
                    @_focus-border: 1px solid @gray-600,
                    @_focus-color: @gray-600,
                );
                box-shadow: none;

            }

            .smsprofile-login-mobile {
                .iti {
                    .iti__flag-container {
                        height: 50px;
                    }
                }
            }

            .iti__flag-container {
                height: 50px;
            }

            .iti--separate-dial-code {
                .iti__selected-flag {
                    background-color: transparent;
                    border-right: 1px solid #ccc;
                }
            }
        }

        .login-right,.block-login-link {
            .block-title {     
                margin-bottom: 20px;

                strong {
                    font-size: 22px;
                    .lib-css(font-weight, @font-weight__bold);
                }
            }

            .block-content {
                max-width: 300px;

                p {
                    .lib-font-size(16);
                }
            }

            .actions-toolbar {
                margin-top: 40px;
            }
        }

        .login-right,
        .block-login-link,.contact-static-block-wrapper {
            margin-left: 0px;
            flex-basis: 100%;
        }

        .fieldset {
            .field.note {
                display: none;
            }

            .legend {
                margin-left: 0;
            }

            >.field {
                margin-bottom: 10px;

                &:not(.choice) {
                    >.label {
                        width: 100%;
                        .lib-font-size(16);
                        .lib-css(font-weight, @font-weight__regular);
                        margin-bottom: 10px;
                        text-align: left;
                    }
                }

                &.choice {
                       display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        min-height: 25px;

                        .checkbox {
                            width: auto;
                            height: auto;
                            margin: -4px 10px 0 0 ;
                        }

                        .label {
                            margin: 0;
                            padding: 0;
                        }
                    &.newsletter {
                       margin-bottom: 20px;
                    }
                    
                }
            }

            .actions-toolbar {
                margin-left: 0;
                display: flex;
                flex-direction: column-reverse;

                .primary {
                    max-width: 200px;
                    width: 100%;
                }

                .secondary {
                    text-align: left;
                    margin-bottom: 30px;
                }
            }
        }
    }

    // SMS Profile styles
    .md-sms-profile {

        // Your custom styles here that extend the parent styles
        .profile-info {
            margin-bottom: 20px;

            .field {
                margin-bottom: 15px;
            }
        }

        .actions-toolbar {
            margin-top: 25px;
        }
    }

    // Additional custom styles that extend the parent

    .md-login-content .mdsms__button-container .btn.active .mdsms__button, .mdsms .mdsms__button-container .btn.active .mdsms__button {
        padding: 8px;
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__s) {
    .customer-account-forgotpassword {
        .control.captcha-image {
            flex-direction: row
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__s) {

    .customer-forgotpassword-page-wrapper,
    .login-container,
    .customer-create-page-wrapper {
        .actions-toolbar {
            .action.primary {
                .lib-font-size(14);
                padding: 12px;
            }
        }
    }
}

// Mobile styles
.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__m) {

    .login-container,
    .customer-create-form-container {
        .lib-vendor-prefix-flex-direction();

        .login-right,
        .block-login-link,.contact-static-block-wrapper {
            margin-top: 30px;

            .block-content {
                max-width: 100%;
            }
        }
    }

    // Mobile-specific styles that extend the parent
    .md-sms-profile {
        .profile-actions {
            flex-direction: column;
        }
    }
}

// Tablet styles
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) {
    .customer-create-page-wrapper {
        .block-login-link,.contact-static-block-wrapper {
            margin-left: 80px;
        }
    }

    .login-container ,.contact-form-and-block-container{

        .login-left,.contact-form-wrapper,.contact-static-block-wrapper,
        .login-right {
            flex-basis: 100%;
        }

        .login-right,.contact-static-block-wrapper,
        .block-login-link {
            margin-left: 30px;
        }

        .login-left {
            input {
                .lib-form-element-input(@_type: input-text,
                    @_color: @gray-600,
                    @_border: 1px solid #CCCCCC,
                    @_border-radius: 3px,
                    @_width: 100%,
                    @_height: 50px,
                    @_focus-border: 1px solid @gray-600,
                    @_focus-color: @gray-600,
                );

            }
        }
    }
    
        .login-left,.form.contact, 
        .forgot-parent,
        .form-create-account {
            padding: 15px 30px;
        }
}

// Desktop styles
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__l) {

    // Desktop-specific styles that extend the parent
    .login-container,
    .customer-forgotpassword-page-wrapper,
    .customer-create-page-wrapper {
        .mdsms {
            .mdsms__button-container {

                .btn-password,
                .btn-otp {
                    .mdsms__button {
                        .lib-font-size(16);
                    }
                }
            }
        }

        .login-right,
        .block-login-link {
            margin-left: 80px;
        }

        .login-left,
        .forgot-parent,
        .form-create-account {
            padding: 50px;
        }
    }
}