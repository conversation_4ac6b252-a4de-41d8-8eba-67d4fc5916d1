// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@mobile-cart-padding: 15px;
@cart-price-color: @color-gray40;
@cart-item-cell-padding-top: 20px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Shopping cart
    //  ---------------------------------------------

    .checkout-cart-index {

        .column.main {
            // .lib-vendor-prefix-display();
            // .lib-vendor-prefix-flex-direction();
            // .lib-vendor-box-align(center);
            // text-align: center;
            // min-height: 500px;
            // padding-top:50px
        }


        .block.shipping{
            .lib-vendor-prefix-display(none);
        } 

        .page-title-wrapper {
            padding-left: 0;
            padding-right: 0;
            margin-top: 30px;
            margin-bottom: 30px;

            .page-title{
                .lib-font-size(46);
            
                .lib-css(font-weight, @font-weight__heavier);
            }
        }
    }

    //  Cart container
    .cart-container {
        .form-cart {
            &:extend(.abs-shopping-cart-items all);

            .actions.main{
                .lib-vendor-prefix-display();
                justify-content: end;
            }

            .action.continue{
                .lib-button-as-link();
                .lib-vendor-prefix-display();
                .lib-css(font-weight, @font-weight__regular);
                gap: 8px;
                text-decoration: underline;
                &::before{
                    content: @icon-arrow-left-thin;
                    font-size: 22px;
                }
            }

            .action.update{
                text-transform: uppercase;
                padding-left: 28px;
                .lib-font-size(18);
                &::before{
                    content: none;
                }
            }
        }
    }

    //  Summary block
    .cart-summary {
        &:extend(.abs-add-box-sizing all);
        .lib-css(background, @sidebar__background-color);
        margin-bottom: @indent__m;
        padding: 15px;

        > .title {
            .lib-font-size(24);
            display: none;
            font-weight: @font-weight__light;
            margin: 12px 0;
        }

        

        .block {
            .fieldset{
                &.coupon{
                position: relative;
                .field {
                    > .label {
                        .lib-vendor-prefix-display(none);
                    }
                }
            }
            .actions-toolbar{
                position: absolute;
                top: 0;
                right: 0;
                height: 100%;
                .lib-vendor-prefix-display();

                .primary.action{
                    // height: 100%;
                    background-color: transparent;
                    color: @color-black;
                    border: none;
                    .lib-css(font-weight, @font-weight__regular);
                    text-transform: capitalize;
                }
            }
            }
            form:not(:last-of-type) {
                .fieldset {
                    margin: 0 0 @indent__m;
                }
            }

            .price {
                font-weight: @font-weight__bold;
            }

            .field {
                margin: 0 0 16px;
            }

            .actions-toolbar {
                > .primary {
                    text-align: left;

                    .action.primary {
                        &:extend(.abs-revert-to-action-secondary all);
                        width: auto;
                    }
                }
            }

            .fieldset.estimate {
                > .legend,
                > .legend + br {
                    &:extend(.abs-no-display all);
                }
            }

            &:extend(.abs-cart-block all);
            .title {
                strong {
                    .lib-font-size(14);
                    .lib-css(font-weight, @font-weight__heavier);
                }
            }

            .item-options {
                margin: 0 0 16px;

                .field {
                    .radio {
                        float: left;
                        margin-top: 4px;
                    }

                    .radio {
                        + .label {
                            display: block;
                            margin: 0;
                            overflow: hidden;
                        }
                    }
                }
            }
        }

        .page-main & {
            .block {
                margin-bottom: 0;
            }
        }

        .checkout-methods-items {
            &:extend(.abs-reset-list all);
            margin: @indent__base 0 0;
            padding: 0;
            text-align: center;

            .action.primary.checkout {
                &:extend(.abs-button-l all);
                width: 100%;
            }

            .item {
                margin-bottom: 0;

                &:last-child {
                    margin-bottom: 0;
                    margin-top: 10px;
                }
                .action.multicheckout {
                    margin-top: 10px;
                    display:inline-block;
                }
            }
        }

        .message {
            padding-left: @indent__base;

            > *:first-child:before {
                display: none;
            }
        }

        &:extend(.abs-adjustment-incl-excl-tax all);
    }

    //  Totals block
    .cart-totals {
        &:extend(.abs-sidebar-totals all);
        tbody,
        tfoot {
            .mark {
                text-align: left;
                padding-left: 0;
                .lib-font-size(16);
            }
        }
        
        tbody{
            td.amount,.grand .amount{
                padding-right: 0; 
                .lib-font-size(16);
            }

            .shipping{
                .mark{
                    padding-bottom: 20px;
                }
            }

            .grand{
                border-top: 1px solid @gray-400;
                .mark,.amount{
                    padding-top: 12px
                }
                .amount,.mark strong{
                .lib-font-size(26);
                .lib-css(font-weight, @font-weight__bold);
                }
            }
        }

        .discount.coupon {
            display: none;
        }
    }

    //  Products table
    .cart {
        &.table-wrapper {
            .cart {
                thead {
                    tr th.col {
                        border-bottom: 3px solid @base-border__color;
                        padding-bottom: 15px;
                        padding-top: 0;
                        padding-left: 0;
                        .lib-font-size(18);
                        .lib-css(font-weight, @font-weight__regular);
                    }
                    .price{
                        text-align: center;
                    }
                }

                tbody {
                    td {
                        border: 0;
                    }
                }

                > .item {
                    border-bottom: @border-width__base solid @gray-100;
                    position: relative;
                    
                    // Add red border for the last cart item
                    &:last-child {
                        border-bottom: 3px solid @base-border__color;
                    }
                }
            }

            .product-item-photo{
                background-color: transparent;
            }

            .product-image-container{
                border: 1px solid @gray-100;
                padding: 5px;
            }

            .col {
                padding-top: 15px;

                &.price,
                &.subtotal,
                &.msrp {
                    padding: @cart-item-cell-padding-top 11px @indent__s;
                    text-align: center;
                    &:extend(.abs-incl-excl-tax all);
                }

                .qty-stepper-container {
                    align-items: center;
                    .qty-btn {
                        &:before {
                            color: #666666;
                        }
                    }
                }

                &.qty {
                    padding: @indent__base 11px @indent__s;
                    text-align: center;

                    .label {
                        &:extend(.abs-visually-hidden all);
                    }

                    .input-text {
                        height: 36px;
                        margin-top: 0;
                        text-align: center;
                        width: 60px;
                    }
                }

                > .price {
                    .lib-css(color, @primary__color__lighter);
                    .lib-font-size(18);
                    font-weight: @font-weight__bold;
                }
            }

            .item-actions {
                td {
                    padding-bottom: 0;
                    padding-left: @mobile-cart-padding;
                    padding-right: @mobile-cart-padding;
                    white-space: normal;
                }
            }

            .item {
                .col.item {
                    display: block;
                    min-height: 75px;
                    padding: 15px @mobile-cart-padding @indent__s 90px;
                    position: relative;
                }
            }

            .actions-toolbar {
                &:extend(.abs-add-clearfix all);
                min-height: 20px;
                padding-bottom: 40px;
                position: relative;

                > .action-edit,
                > .action-delete {
                    position: absolute;
                    right: 16px;
                    top: 0;
                    .lib-icon-font(
                    @icon-edit,
                    @_icon-font-size: 40px,
                    @_icon-font-line-height: 20px,
                    @_icon-font-text-hide: true,
                    @_icon-font-color: @gray-600,
                    @_icon-font-color-hover: @primary__color,
                    @_icon-font-color-active: @minicart-icons-color
                    );
                }

                > .action-delete {
                    &:extend(.abs-action-button-as-link all);
                    right: 0;
                    .lib-icon-font-symbol(
                    @_icon-font-content: @icon-remove
                    );

                    &:hover {
                        .lib-css(text-decoration, @link__text-decoration);
                    }
                }
            }

            .action {
                margin-right: @indent__m;

                &:last-child {
                    margin-right: 0;
                }

                &.help.map {
                    &:extend(.abs-action-button-as-link all);
                    font-weight: @font-weight__regular;
                }
            }

            .product {
                &-item-photo {
                    display: block;
                    left: @mobile-cart-padding;
                    max-width: 65px;
                    padding: 0;
                    position: absolute;
                    top: 15px;
                    width: 100%;
                }

                &-item-details{
                    margin-left: 15px;
                    text-align: left;
                }

                &-item-name {
                    .lib-font-size(16);
                    display: block;
                    margin: 0;

                    a{
                        color: @color-black;
                    }
                }
            }

            .gift-registry-name-label {
                &:after {
                    content: ':';
                }
            }

            //  Product options
            .item-options {
                margin-bottom: 0;
                &:extend(.abs-product-options-list all);
                &:extend(.abs-add-clearfix all);
            }

            .product-item-name + .item-options {
                margin-top: @indent__base;
            }

            .item-options{
                dt{
                    color: @gray-600;
                    .lib-css(font-weight, @font-weight__regular);
                }
            }

            .cart-tax-total {
                &:extend(.abs-tax-total all);
                &-expanded {
                    &:extend(.abs-tax-total-expanded all);
                }
            }

            .product-image-wrapper {
                &:extend(.abs-reset-image-wrapper all);
            }

            .action.configure {
                display: inline-block;
                margin: @indent__s 0 0;
            }

            .item .message {
                margin-top: 12px;
            }
        }
    }

    //  Products pager
    .cart-products-toolbar {
        .toolbar-amount {
            left: inherit;
            margin: @indent__m 0 15px;
            padding: 0;
            position: relative;
            text-align: center;
            top: inherit;
        }
    }

    .cart-products-toolbar-top {
        border-bottom: @border-width__base solid @border-color__base;
    }

    //  Discount
    .cart-discount {
        border-bottom: @border-width__base solid @border-color__base;
        clear: left;
        &:extend(.abs-discount-block all);
    }

    //  Empty cart
    .cart-empty {
        padding-left: @layout__width-xs-indent;
        padding-right: @layout__width-xs-indent;
    }

    .cart-tax-info,
    .cart .cart-tax-info {
        + .cart-tax-total {
            display: block;
        }
    }

    .cart.table-wrapper,
    .order-items.table-wrapper {
        
        .col.subtotal,
        .col.msrp {
            text-align: right;
        }

        .col.price,
        .col.qty{
            text-align: center;
        }

        .col{
            &.price,&.subtotal{
            .price-excluding-tax{
                .lib-font-size(16);
            }
        }
        }

        .field.qty{
            .lib-vendor-prefix-display();
            justify-content: center;
            position: relative;

            .input-text.qty{
                background-color: transparent;
            }

            .input-text.qty.mage-error{
                position: static;
            }

            .mage-error{
                position: absolute;
                top: 40px;
                left: 0;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .cart {

        &.table-wrapper {
            .field.qty .mage-error{
                white-space: normal;
            }           
        }
        .table.items {
            .col.item,
            .item-actions td {
                &:extend(.abs-col-no-prefix all);
            }


            .col.qty {
                text-align: center;
            }

            tbody > tr > td:last-child {
                &:extend(.abs-no-border-bottom-top all);
            }
        }
    }
    .cart-totals {
        .totals {
            &:extend(.abs-sidebar-totals-mobile all);
        }
    }

    .block{
        &.crosssell{
            .product-items{
                .product-item-info {
                    position: relative;
                }
                .action.primary{
                    padding: 10px;
                    .lib-font-size(14);
                }

                .actions-secondary{
                    position: absolute;
                    top: 5px;
                    right: 5px;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    //  Cart container
    .cart-container {
        .form-cart {
            .actions.main {
                .lib-vendor-prefix-display();
                .lib-vendor-prefix-flex-direction(column);
                .lib-vendor-box-align(center);

                .clear,
                .continue {
                    .lib-css(margin, 0 0 @indent__m 0);
                }
            }
        }
    }

    .cart-discount {
        border-bottom: @border-width__base solid @border-color__base;
    }

    .cart {
        &.table-wrapper {
            border-top: @border-width__base solid @border-color__base;
            thead {
                .col {
                    &.item,
                    &.qty,
                    &.price,
                    &.subtotal,
                    &.msrp {
                        display: none;
                    }
                }
            }

            .col {
                &.qty,
                &.price,
                &.subtotal,
                &.msrp {
                    box-sizing: border-box;
                    display: block;
                    float: left;
                    white-space: nowrap;
                    width: 33%;
                    margin-top: 10px;

                    &[data-th]:before {
                        content: attr(data-th);
                        display: block;
                        font-weight: @font-weight__semibold;
                        padding-bottom: 10px;
                    }
                }

                &.qty{
                    text-align: center;
                }

                &.msrp {
                    white-space: normal;
                }
            }

            .item .col.item {
                padding-bottom: 0;
            }

            tbody > tr > td:last-child {
                border: 0;
            }
        }
    }

    .cart-totals {
        .table-wrapper {
            border-top: 0;
        }

        .totals {
            tbody > tr:not(:last-child) > td:last-child {
                border: 0;
            }
        }
    }

    .cart.table-wrapper,
    .order-items.table-wrapper {
        .col.price,
        .col.subtotal,
        .col.msrp {
            text-align: left;
        }

        .col.subtotal{
            text-align: right;
        }
    }

}

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    //  Cross sell
    .block {
        &.crosssell {
            margin-top: 70px;

            .product-items{
                .lib-vendor-prefix-display();
                flex-wrap: wrap;
            }

            .product-item-info {
                width: auto;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .cart-container {
        .block.crosssell {
            .products-grid {
                .product-item-actions {
                    margin: 0 0 @indent__s;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .checkout-cart-index {
       

        .page-title-wrapper {
            &:extend(.abs-revert-side-paddings all);
        }
    }

    //  Cart container
    .cart-container {
        &:extend(.abs-add-clearfix-desktop all);
        .form-cart {
            &:extend(.abs-shopping-cart-items-desktop all);
            width: ~'calc((100% - 32.35%) - 50px)';
            padding-right: 50px;
            .actions.main {
                text-align: right;
            }
        }

        .widget {
            float: left;

            &.block {
                margin-bottom: @indent__base;
            }
        }
    }

    //  Summary block
    .cart-summary {
        .lib-layout-column(2, 2, 32.25%);
        padding:25px;
        position: relative;

        > .title {
            display: block;
            .lib-font-size(36);
            .lib-css(font-weight, @font-weight__heavy);
            margin-top: 0;
            margin-bottom: 20px;
        }

        .fieldset {
            .actions-toolbar {
                margin-left: 0;

                > .secondary {
                    float: none;
                }
            }
        }

        .block {
            > .title {
                padding-left: 0;

                &:after {
                    right: 3px;
                }
            }

            .content {
                &:extend(.abs-revert-side-paddings all);
            }

            .fieldset {
                .field {
                    .lib-form-field-type-revert(@_type: block);
                    margin: 0 0 @indent__s;
                }
            }
        }

        .checkout-methods-items {
            padding: 0;
        }
    }

    //  Products table
    .cart {
        &.table-wrapper {
            .items { // Google Chrome version 44.0.2403.107 m fix
                min-width: 100%;
                width: auto;
            }

            tbody td {
                padding-top: @cart-item-cell-padding-top;
            }

            .item {
                .col.item {
                    padding: @cart-item-cell-padding-top 8px @indent__base 0;
                }
            }

            .item-actions td {
                padding: 0;
            }

            .product {
                &-item-photo {
                    display: table-cell;
                    max-width: 100%;
                    padding-right: @indent__base;
                    position: static;
                    vertical-align: top;
                    width: 1%;
                }

                &-item-details {
                    display: table-cell;
                    padding-bottom: 0px;
                    vertical-align: top;
                    white-space: normal;
                    width: 99%;
                }
            }
        }
    }

    //  Products pager
    .cart-products-toolbar {
        margin: 2px 0 0;

        .toolbar-amount {
            line-height: 30px;
            margin: 0;
        }

        .pages {
            float: right;

            .item {
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .cart.table-wrapper {
        .cart-products-toolbar {
            + .cart {
                thead {
                    tr {
                        th.col {
                            padding-bottom: @indent__s;
                            padding-top: @indent__s;
                        }
                    }
                }
            }
        }

        .cart {
            + .cart-products-toolbar {
                margin-top: @indent__m;
            }
        }
    }

    //  Discount
    .cart-discount {
        &:extend(.abs-discount-block-desktop all);
        .lib-layout-column(2, 1, @layout-column-checkout__width-main);
        border: 0;
        box-sizing: border-box;
        padding-right: 4%;

        .block {
            .title {
                &:after {
                    display: inline;
                    margin-left: @indent__s;
                    position: static;
                }
            }

            &.discount {
                width: auto;
            }

            .actions-toolbar {
                width: auto;
            }
        }
    }

    //  Empty cart
    .cart-empty {
        &:extend(.abs-revert-side-paddings all);
    }

    //  Cross sell
    .block {
        &.crosssell {
            .lib-layout-column(2, 1, @layout-column-checkout__width-main);
            &:extend(.abs-add-box-sizing-desktop all);
            padding: 0;
            width: 100%;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .cart-summary{
       padding: 40px 50px; 
    }
}
