// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@account-nav-background: @sidebar__background-color;
@account-nav-color: false;

@account-nav-current-border: 4px solid transparent;
@account-nav-current-border-color: @color-black;
@account-nav-current-color: false;
@account-nav-current-font-weight: @font-weight__semibold;

@account-nav-delimiter__border-color: @color-gray82;

@account-nav-item-hover: @color-gray91;

@_password-default: @color-gray-light01;
@_password-weak: #ffafae;
@_password-medium: #ffd6b3;
@_password-strong: #c5eeac;
@_password-very-strong: #81b562;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {


    .customer-address-form .address-map {
        display: block;
        width: 100%;
        margin: 0 0 20px;
    }
    .customer-address-form {
        .address-map {
            button.button.action.action-pin-location {
                padding: 15px 10px;
                font-size: 16px;
                line-height: 21px;
                &:focus {
                    background-color: @color-black;
                    color: @color-white;
                }
            }
        }
    }

    .customer-address-form {
        .address-map {
            button.button.action.action-confirm-location {
                border: 1px solid #000;
            }
        }
    }



    .magento_invitation-customer_account-create {
        .columns {
            .form-create-account {
                .lib-vendor-prefix-flex-direction(column);
            }
        }
        .fieldset {
            legend.legend {
                margin: 0 0 20px;
                font-size: 22px;
                color: #000;
                font-weight: bold;
            }
        }
    }

    .customer-account-create,
    .magento_invitation-customer_account-create {
        .ui-datepicker {
            .ui-datepicker-title {
                background: transparent;
                padding: 0;
                select {
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    text-indent: 1px;
                    text-overflow: '';
                    background-position: 93% 50%;
                    color: #000;
                    border: none;
                    padding: 5px 10px;
                    height: auto;
                }
                .ui-datepicker-year {
                    box-shadow: none;
                    font-weight: bold;
                }
                .ui-datepicker-month {
                    box-shadow: none;
                    font-weight: bold;
                }
            }
            .ui-datepicker-calendar {
                th {
                    background: #f1f1f1;
                    border: #ccc 1px solid;
                    padding: 8px;
                    text-align: center;
                }
                td {
                    border: #ccc 1px solid;
                a {
                    padding: 8px;
                    text-align: center;
                }
                }
                .ui-state-active {
                    background: #000;
                    color: #fff;
                }
                .ui-state-hover {
                    background: #000;
                    color: #fff;
                }
            }
            .ui-datepicker-prev {
                span {
                    margin-left: 0px;
                        top: 15px;
                        border-color: transparent #000 transparent transparent;
                }
            }
            .ui-datepicker-next {
                span {
                        margin-left: -7px;
                        top: 15px;
                        border-color: transparent transparent transparent #000;
                    }
                }
            }
    }

    fieldset[name="loyalty_field_attributes"] {
        border: none;
        padding: 0;
        .control.customer-dob {
            input {
                width: 100%;
                margin: 0;
                padding-right: 50px;
            }
            button.ui-datepicker-trigger {
                position: absolute;
                right: 10px;
                top: 0;
                &:before {
                    font-size: 28px;
                    color: #000;
                    line-height: 45px;
                }
            }
        }
    }

    .customer-account-index .bar-wrapper.top.reward-points {
        margin: 0;
    }

    .bar-wrapper.top {
        &.reward-points {
            width: 100%;
            margin: 0 0 20px;
            .free-shipping-bar {
                a {
                    font-weight: @font-weight__semibold;
                }
                &:before {
                    font-family: 'galeries-lafayetee-icon';
                    content: "\e925";
                    font-weight: 400;
                    font-size: 20px;
                    display: inline-block;
                    line-height: 16px;
                    background-image: none;
                }
            }
        }
    }

    .form-create-account {
        .sms-profile-register {
            .fieldset {
                .field.field-name-customer_mobile {
                    .control {
                        button.verif_otp_login.action.primary {
                            position: absolute;
                            right: 0;
                            top: 0;
                            padding: 13px 10px;
                            width: 138px;
                        }
                    }
                }
            }
        }
    }

    .customer-account-create,
    .magento_invitation-customer_account-create {
        .page.messages {
            .message-error.error.message a {
                color: #e02b27;
                font-weight: 700;
                text-decoration: underline;
                &:hover {
                    color: @color-black;
                }
            }
        }
        .columns .field label.label {
            padding-bottom: 8px;
            margin-bottom: 0 !important;
            line-height: 19px;
        }
        .form-create-account .sms-profile-register .fieldset .field {
            margin-bottom: 15px;
        }

        .form-create-account .sms-profile-register .fieldset .field.fullname {
            margin: 0;
        }
    }

    .customer-account-create .account select#prefix,
    .magento_invitation-customer_account-create select#prefix {
        background-position: 98% 50%;
    }

    .modal-popup._show .modal-body-content {
        display: block;
    }

    .modal-popup.cancel-order-modal.modal-slide {
        header.modal-header {
            padding: 0 0 10px;
            h1 {
                text-transform: capitalize;
                line-height: 25px;
                font-size: 24px;
                line-height: 30px;
            }
        }
        .modal-inner-wrap {
            width: 400px;
            box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.29);
            border-radius: 5px;
            background-color: @color-white;
            padding: 30px 52px;
        }
        .modal-content {
            padding: 0;
            font-size: 16px;
            line-height: 22px;
            color: #000;
            h3 {
                line-height: 24px;
                font-size: 18px;
                margin: 0 0 5px;
              
                font-weight: 700;
            }
            p {
                margin-bottom: @indent__xs; 
                margin-top: @indent__s;
            }
        }
        footer.modal-footer {
            border-top: none;
            text-align: center;
            padding-top: 0;
            margin-top: @indent__base;
            .lib-vendor-prefix-display(flex);
            .lib-css(align-items, center, @_prefix: 1);
            .lib-css(justify-content, center, @_prefix: 1);
            gap: 10px;
            padding: 0;
            button {
                width: 49%;
                font-weight: 700;
                font-size: 14px;
                line-height: 16px;
                padding: 14px;
            }
        }
        .modal-inner-wrap {
            transform: translate(-50%, -100%) !important;
            top: 0;
            left: 50%;
            margin: 0;
         
            padding: @indent__base;
            position: fixed;
            height: auto;
        }
        &._show {
            .modal-inner-wrap {
                transform: translate(-50%, -50%) !important;
                top: 50%;
                left: 50%;
                margin: 0;
                position: fixed;
            }
        }
    }

    .customer-account-logoutsuccess {
        .column.main {
            .lib-vendor-prefix-display();
            .lib-vendor-prefix-flex-direction();
            .lib-vendor-box-align(center);
            text-align: center;
            min-height: 500px;
            padding-top:50px
        }   
    }
    .account {
        .column.main {
           .page-title-wrapper .note-msg {
                max-width: 50%;
            }
            .actions-toolbar.order-actions-toolbar {
                .cancel-order {
                    background: #fff;
                    border: 1px solid #000000;
                    color: #000000;
                    padding: 8px 10px;
                    font-weight: 400;
                    font-size: 16px;
                    border-radius: 3px;
                    text-decoration: none;
                   
                    &:hover {
                        background-color: @color-black;
                        color: @color-white;
                    }
                    &:after {
                        display: none !important;
                    }
                }
            }
        }
    }

    .page-wrapper {
        .account .fieldset > .field.choice .label,
        .account .fieldset > .fields > .field.choice .label {
            color: @color-black;
        }
    }

    .smsprofile-login-option {
        .login-option-tab {
            .login-email,
            .login-mobile {
                &.field {
                    .lib-vendor-prefix-flex-basis(48%);
                    margin: 0;
                }
            }
        }
    }

    .form-login {
        .smsprofile-login-mobile {
            button.send_otp_login.action.primary {
                margin-top: 34px;
            }
        }
    }

    .smsprofile-login-mobile {
        position: relative;
        button.send_otp_login.action.primary {
            top: 0;
            margin: 0;
        }
    }

    .form.form-newsletter-manage {
        .field.choice {
            width: 100% !important;
            margin: -10px 0 0 !important;
        }
    }

    .account {
        .column.main {
            .fieldset > .field.choice {
                width: 100%;
                label.label {
                    text-transform: none;
                    font-size: 14px;
                    padding-top: 2px;
                }
            }
        }
        .transaction-history tbody tr {
            border-bottom: none;
        }

    }

    .customer-account-forgotpassword,
    .customer-account-createpassword,
    .magento_invitation-customer_account-createpassword {
        .page-main > .page-title-wrapper .page-title {
            margin: 0 0 @indent__base;
        }
        .columns {
            .field.mobile {
                position: relative;
                padding-right: 140px;
                width: 100%;
                button.send_otp_login.action.primary {
                    top: 0;
                    margin: 0;
                }
            }
        }
    }

    .iti-mobile {
    .iti__country-list {
        max-height: inherit !important;
        width: 100%;
            position: relative;
            padding: 0px;
        margin: 0px;
    }
        .iti--container {
            top: auto !important;
            bottom: 0px !important;
            left: 0px !important;
            right: 0px !important;
            position: fixed;
                    &:after {
                    content: "";
                        background-color: rgba(79, 78, 82, 0.55);
                    bottom: 0;
                    left: 0;
                    position: fixed;
                    right: 0;
                    top: 0;
                }
        }
    }

    .login-container  {
        .form-field__control {
            &.control.mobile {
                padding-right: 140px;
            }
        }
    }

    form.form-edit-account .field.customer_mobile.required .control {
        position: relative;
        button.send_otp_login.action.primary {
            position: absolute;
            right: 0;
            top: 0;
            margin: 0 !important;
            height: 44px;
        }
        .iti {
            width: 100%;
        }
        input[type="text"] {
            padding-right: 140px;
            width: 100%;
        }
    }





    .customer-address-index,
    .customer-address-form {
        h1.page-title {
            margin-bottom: @indent__base !important;
        }
    }

    .customer-address-form {
        .form-address-edit {
            fieldset.fieldset {
                .field.street {
                    width: 100%;
                }
            }
        }
    }

    .account {
        .column.main {
            .fieldset > .field.street {
                .field.primary {
                    label.label {
                        display: block;
                        font-size: 16px;
                        line-height: 19px;
                        color: #000000;
                        font-weight: 400;
                        margin: 0 0 7px;
                        clip: initial;
                        height: auto;
                        width: auto;
                        position: relative;
                        overflow: initial;
                    }
                }
                .nested {
                    .lib-vendor-prefix-display(flex);
                    flex-wrap: wrap;
                    .lib-css(justify-content, space-between, @_prefix: 1);
                    .field.additional {
                        width: 49%;
                        label.label {
                            display: block;
                            font-size: 16px;
                            line-height: 19px;
                            color: #000000;
                            font-weight: 400;
                            margin: 0 0 7px;
                            clip: initial;
                            height: auto;
                            width: auto;
                            position: relative;
                            overflow: initial;
                        }
                        &:last-child {
                            width: 100%;
                        }
                    }
                }
            }
        }
        .fieldset > .field.choice .label,
        .fieldset > .fields > .field.choice .label {
            font-weight: @font-weight__regular;
            color: @color-black;
            text-transform: none;
        }
        .fieldset > .field > .label,
        .fieldset > .fields > .field > .label {
            margin-bottom: 7px;
            font-size: 16px;
            line-height: 19px;
            color: @color-black;
            font-weight: @font-weight__regular;
        }
    }








    .form.form-edit-account .fieldset-fullname .fields {
        .lib-vendor-prefix-display(flex);
        gap: 12px;
        .field {
            margin: 0;
            &.field-name-prefix {
                max-width: 78px;
            }
        }
    }

    .account .form-address-edit .fieldset > .message.info {
        width: 100%;
        margin: 0;
    }
    #max-emails-message {
        margin: 5px 0 10px;
        width: 100%;
    }



    .form.form-add-invitations  {
        #invitations-options {
            width: 100%;
        }
      





        
    }





        .account {
            .ui-datepicker {
                width: 300px;
                background: #f1f1f1;

        .ui-datepicker-title {
            .ui-datepicker-year {
                box-shadow: none;
                font-weight: bold;
            }
            .ui-datepicker-month {
                box-shadow: none;
                font-weight: bold;
            }
        }
        .ui-datepicker-calendar {
            th {
                background: #f1f1f1;
                border: #ccc 1px solid;
                padding: 8px;
                text-align: center;
            }
            td {
                border: #ccc 1px solid;
            a {
                padding: 8px;
                text-align: center;
            }
            }
            .ui-state-active {
                background: #000;
                color: #fff;
            }
            .ui-state-hover {
                background: #000;
                color: #fff;
            }
        }
        .ui-datepicker-prev {
            span {
                margin-left: 0px;
                    top: 25px;
                    border-color: transparent #000 transparent transparent;
            }
        }
        .ui-datepicker-next {
            span {
                    margin-left: -7px;
                    top: 25px;
                    border-color: transparent transparent transparent #000;
                }
            }
        }
    }

    .form {
        .fieldset {
            .field {
                label.label {
                    margin-bottom: 5px;
                    color: #000;
                    font-weight: 500;
                    display: inline-block;
                    font-size: 14px;
                    text-transform: none;
                    width: auto;
                }
                &:not(.customer-account-login) &{

                    max-width: 350px;
                }
            }
        }
    }
    .form.form-edit-account {
        fieldset.fieldset {
            margin: 0px;

        }
    }


.box.box-address-billing, .box.box-address-shipping {
    .box-content {
        line-height: 22px;
    }
}

.table-wrapper.additional-addresses{
    table {
            .actions{
                a:last-child:after {
                    border-right: 0px;
                    padding-right: 0px;
                    margin-right: 0px;
                }
                a {
                    &:after {
                    border-right: 1px solid #000;
                    margin-right: 6px;
                    padding-right: 10px;
                    content: "";
                    height: 11px;
                    display: inline-block;
                    line-height: normal;
                    }
                }

            }
            }
}
.block.block-dashboard-orders{
           .block-content{
            .actions{
                a:last-child:after {
                    border-right: 0px;
                }
                a {
                    &:after {
                    border-right: 1px solid #000;
                    margin-right: 5px;
                    padding-right: 5px;
                    content: "";
                    height: 11px;
                    display: inline-block;
                    line-height: normal;
                    }
                }

            }
        }
}
.table-wrapper.orders-history{
            .actions{
                a:last-child:after {
                    border-right: 0px;
                }
                a {
                    &:after {
                    border-right: 1px solid #000;
                    margin-right: 5px;
                    padding-right: 5px;
                    content: "";
                    height: 11px;
                    display: inline-block;
                    line-height: normal;
                    }
                }

            }
}
    .block.block-dashboard-info{
        .block-content {
            .box-actions{
                a.change-password {
                    &:before {
                    border-right: 1px solid #000;
                    margin-right: 5px;
                    padding-right: 5px;
                    content: "";
                    height: 11px;
                    display: inline-block;
                    line-height: normal;

                    }
                }

            }
        }
    }










.account {
        select {
            -webkit-appearance: none;
            -moz-appearance: none;
            text-indent: 1px;
            text-overflow: '';
            background-position: 97% 50%;
        }

        .column.main {
        .ui-datepicker {
        width: 300px;
            .ui-datepicker-prev {
            span {
                margin-left: 0px;
                border-color: #000;
            }
        }

        }

            

    

            .actions-toolbar.order-actions-toolbar {
                    display: flex;
                    align-items: end;
                    justify-content: end;
                    margin-bottom: -16px;
                        .amcorder-button.-link:before {
                            border-right: 1px solid #000;
                            margin-right: 10px;
                            padding-right: 10px;
                            height: 11px;
                            display: inline-block;
                            line-height: 20px;
                            vertical-align: middle;
                            border-radius: 0;
                            content: "";
                        }
                .actions {

                        a:last-child:after {
                        border-right: 0px;
                        padding-right: 0px;
                        margin-right: 0px;
                        }
           

                }
            }
       


            .block {
                margin-bottom: @indent__base;
                .block-title{
                   
                    border-bottom: 1px solid #ccc;
                    padding-bottom: 15px;
                    strong{
                        font-family: @font-family-name__base !important;
                    }
                }
            }
        }
    }
 









    

    .block-addresses-list {
        .items.addresses {
            > .item {
                margin-bottom: @indent__base;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }




    .box-billing-address,
    .box-shipping-address,
    .box-information,
    .box-newsletter {

        .box-content {
            .lib-wrap-words();
            line-height: 26px;
        }
    }

    //  Full name fieldset
    .fieldset {
        .fullname {
            &.field {
                > .label {
                    &:extend(.abs-visually-hidden all);

                    + .control {
                        width: 100%;
                    }
                }
            }

            .field {
                &:extend(.abs-add-clearfix all);
            }
        }
    }

    //
    //  My account
    //  ---------------------------------------------


  

    .form.form-edit-account {
            fieldset.fieldset.info, fieldset.fieldset.password{
                margin-bottom: 25px;
            .field.choice.field-privacy-checkbox label {
            display: inline-block !important;
            }
            }
        }
        fieldset.fieldset.additional_info {
            fieldset {
            margin: 0px;
            .customer_mobile {
                margin: 0px 0 10px 0;
                padding: 0px;
                label {
                    width: 100%;
                }
                .iti__flag-container {
                    height: 44px;
                }
            }
            }
        }

        .fieldset > .field {
        margin: 0 0 15px;
        }
        
            .block{
                .account .column.main & {
                    margin-bottom: 20px;

                    .block-title {
                        strong{
                            font-size: 22px;
                        }
                    }
                }
            }
                    .box {
                strong{
                    span{
                    font-size: 18px;
                    }
                }
            }
            table th {
            color: @color-black;
            }
            table {
                th {
                color: @color-black;
                }
                th,td{
                    padding: 12px 12px 12px 0px;
                }
            }

            form {
                .fieldset {
                    legend.legend {
                        margin-left: 0px;
                    }
                }
            }

            .actions-toolbar {
                margin: 0px;
                .account .column.main & {
                    .primary {
                       .action {
          
                          
                        }
                    }

                    .secondary {
                           .action {
                        
                            }
                        }
                }
            }
            h2 {
                margin-top: 0;
            }





            .block:not(.widget) {
                &:extend(.abs-account-blocks all);
            }
        }

        .sidebar-additional {
            margin-top: 0px;
        }

        .table-wrapper {
            &:last-child {
                margin-bottom: 0;
            }

            .action {
                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .table-return-items {
            .qty {
                .input-text {
                    &:extend(.abs-input-qty all);
                }
            }
        }
   

    //
    //  Account navigation
    //  ---------------------------------------------

    .account-nav {
        .title {
            &:extend(.abs-visually-hidden all);
        }

        .content {
            .lib-css(background, @account-nav-background);
            padding: 15px 0;
        }

        .item {
            margin: 3px 0 0;

            &:first-child {
                margin-top: 0;
            }

            a,
            > strong {
                .lib-css(color, @account-nav-color);
                border-left: 3px solid transparent;
                display: block;
                padding: @indent__xs 18px @indent__xs 15px;
            }

            a {
                text-decoration: none;

                &:hover {
                    .lib-css(background, @account-nav-item-hover);
                }
            }

            &.current {
                a,
                strong {
                    .lib-css(border-color, @account-nav-current-border-color);
                    .lib-css(color, @account-nav-current-color);
                    .lib-css(font-weight, @account-nav-current-font-weight);
                }

                a {
                    .lib-css(border-color, @account-nav-current-border-color);
                }
            }

            .delimiter {
            }
        }
    }
    
    //
    //  Blocks & Widgets
    //  ---------------------------------------------

    .block {
        &:extend(.abs-margin-for-blocks-and-widgets all);
        .column.main & {
            &:last-child {
                margin-bottom: 0;
            }
        }

        .title {
            margin-bottom: @indent__s;

            strong {
                .lib-heading(h4);
                .account .column.main & {
                    font-size: @h3__font-size;
                }
            }
        }

        p:last-child {
            margin: 0;
        }

        .box-actions {
            margin-top: @indent__xs;
        }
    }
    
    //
    //  Password Strength Meter
    //  ---------------------------------------------

    .field.password {
        .control {
            .lib-vendor-prefix-display();
            .lib-vendor-prefix-flex-direction();

            .mage-error {
                .lib-vendor-prefix-order(2);
            }

            .input-text {
                .lib-vendor-prefix-order(0);
                z-index: 2;
            }
        }
    }

.password-strength-meter {
    background-color: @_password-default;
    color: #666666;
    height: 38px;
    line-height: 38px;
    padding: 0 0 0 4px;
    font-size: 14px;
    position: relative;
    z-index: 1;

    &:before {
        content: '';
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        z-index: -1;
    }

    .password-none & {
        &:before {
            background-color: @_password-default;
            width: 100%;
        }
    }

    .password-weak & {
        &:before {
            background-color: @_password-weak;
            width: 25%;
        }
    }

    .password-medium & {
        &:before {
            background-color: @_password-medium;
            width: 50%;
        }
    }

    .password-strong & {
        &:before {
            background-color: @_password-strong;
            width: 75%;
        }
    }

    .password-very-strong & {
        &:before {
            background-color: @_password-very-strong;
            width: 100%;
        }
    }
}

    





    





//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {


    .customer-address-form .address-map #searchGoogleMap {
        width: 100% !important;
    }


    .customer-account-createpassword  .page-main .page-title-wrapper,
    .magento_invitation-customer_account-createpassword .page-main .page-title-wrapper {
        max-width: 100%;
        margin: 0 auto;
        h1.page-title {
            font-size: 32px;
        }
    }

    .form.form-edit-account fieldset.fieldset {
        margin: 0px;
        width: 100%;
    }


    #additional-addresses-table tbody td.col {
        &:before {
            min-width: 110px;
        }
        &:nth-child(2n+1) {
            background: #f4f4f4;
            width: 100%;
            .lib-vendor-prefix-display(@_value: flex);
            .lib-vendor-prefix-flex-wrap (@_value: nowrap);
            max-width: initial;
        }
    }

    .account {
        .block-collapsible-nav .content.active {
            display: block;
            max-height: 377px;
            overflow-y: auto;
            border: none;
            box-shadow: 0 5px 15px #ccc;
            
        }
        .form.form-add-invitations {
            > fieldset.fieldset legend.legend {
                font-size: 16px;
            }
        }
        .points-cards {
            .row {
                margin: 0 -@indent__xs;
                .card {
                    padding: @indent__xs;
                }
            }
        }
        .block-collapsible-nav {
            z-index: 11;
        }
        .giftcard-account {
            padding: 20px;
            max-width: 100%;
        }
        .storecredit {
            .lib-vendor-prefix-flex-direction(column);
            gap: 15px;
            .block {
                width: 100%;
                padding: @indent__base;
                margin-top: @indent__s;
            }
        }
        .block.block-order-details-view .box,
         .block.block-dashboard-info .box,
         .block.block-dashboard-addresses .box,
         .block.block-addresses-default .box,
         .block.block-addresses-list .box {
            padding: 15px;
        }
        .page-wrapper {
            .column.main {
                a.action.view {
                    margin-right: 0 !important;
                }
                .fieldset > .field.street .nested .field.additional {
                    width: 100%;
                }
            }
        }
    }

    .customer-account-index {
        .page-title-wrapper {
            margin-top: @indent__s + @indent__xs;
        }
    }

    .account {
        .column.main {
            .page-title-wrapper {
                .page-title {
                    margin-top: @indent__s + @indent__xs;
                }
            }

            .toolbar{
                .pager {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    margin-top: 15px;
                }
            }
            .limiter-options{
                width: 70px;
            }
        }
    }

    .block-collapsible-nav {
        .title.block-collapsible-nav-title {
            background: #000;
            color: #fff;
            margin:0;
            border: none;
            strong {
               
                color: @color-white;
                font-size: 18px;
                font-weight: 600;
                text-transform: uppercase;
            }
        }
        .content.active {
            margin: -15px 15px 0;
        }
    }

}


.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {



    .account {
        .column.main {
            .actions-toolbar.order-actions-toolbar {
                display: flex;
                align-items: start;
                justify-content: start;
                margin-bottom: 0;

                    .actions{
                        display: flex;
                        flex-direction: column;
                        gap: 10px;
                        margin-bottom: 10px;
                    }
            }
        }
    }




        .account {
            .column.main,
            .sidebar-additional {
                margin: 0;
            }
        }


    .account {
        .column.main {
            .toolbar {
                .toolbar-amount, .pages, .limiter{
                margin: 0px 0px 15px 0px;
                float: none;
                }
                
            }

            
        }
    }


}




.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .block.block-dashboard-addresses{
            .box.box-billing-address {
            border-bottom: 1px solid #ccc;
            padding-bottom: 20px;
        }
    }

    .block.block-dashboard-orders {
        table {
            tr {
                border-bottom: 1px solid#ccc;
            }
        }
    }

    .table-wrapper.orders-history{
        table {
            tr {
                border-bottom: 1px solid#ccc;
            }
        }
    }





    button.verif_otp_login.action.primary {
        padding: 14px 48px;
        font-size: 16px;
    }
    p.resend-wait .time {
        font-size: 14px;
    }
    .smsprofile-login-mobile {
        .fieldset {
            >.field {
                .resendlink {
                    .resendotp {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .order-details-items {
        .order-title {
            margin: 15px 0px 0px 0px;
        }
        .table-order-items {
            tr {
                td {
                    .product-item-name {
                        height: auto;
                    }
            .price-excluding-tax {
                        line-height: 14px;
                    }
                    .cart-price{
                        font-size: 14px;
                    }
                }
            }
        }

    }






    
    


    //  My account
    .account {
        .sidebar{
        .block-collapsible-nav {
            .title {
                &:after {
                    right: 15px;
                    top: 13px;
                    font-size: 18px;
                }
            }
        }
    }
        .column.main {
            .page-title-wrapper {
                .page-title {
                    font-size: 24px;
                }
            }

            .block-addbysku {
                .box {
                    padding: 20px;
                    min-height: auto;
                    .action.remove {
                        &:before {
                            font-size: 16px;
                        }
                    }
                }
            }

        }
    }


    .account {
        .messages {
            margin-bottom: 0;
        }

        .toolbar {
            &:extend(.abs-pager-toolbar-mobile all);
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {




    .table-wrapper.table-order-items.invoice .col.qty {
        text-align: center;
    }



    .points-cards {
        margin: 0 0 20px -10px;
    }

    .account .row .card {
        padding: 10px;
    }



    

    //  Full name fieldset
    .fieldset {
        .fullname {
            .field {
                .label {
                    .lib-css(margin, @form-field-type-label-inline__margin);
                    .lib-css(padding, @form-field-type-label-inline__padding);
                    .lib-css(text-align, @form-field-type-label-inline__align);
                    .lib-css(width, @form-field-type-label-inline__width);
                    box-sizing: border-box;
                    float: left;
                }

                .control {
                    .lib-css(width, @form-field-type-control-inline__width);
                    float: left;
                }
            }
        }
    }



    //
    //  My account
    //  ---------------------------------------------

    .account.page-layout-2columns-left {
        .sidebar-main,
        .sidebar-additional {
            width: 22.3% !important;
            margin-top: 40px;
            padding-right: 15px !important;
            max-width: 100% !important;
        }

        .column.main {
            width: 77.7% !important;
            margin-top: 40px;
            padding-left: 15px !important;
            font-size: 15px;
        }
    }

    .account {
        .column.main {
            .block:not(.widget) {
                .block-content {
                    &:extend(.abs-add-clearfix-desktop all);

                    .box {
                        &:extend(.abs-blocks-2columns all);
                    }
                }
            }
        }

        .toolbar {
            &:extend(.abs-pager-toolbar all);
        }
    }

    .block-addresses-list {
        .items.addresses {
            &:extend(.abs-add-clearfix-desktop all);
            font-size: 0;

            > .item {
                display: inline-block;
                font-size: @font-size__base;
                margin-bottom: @indent__base;
                vertical-align: top;
                width: 48.8%;

                &:nth-last-child(1),
                &:nth-last-child(2) {
                    margin-bottom: 0;
                }

                &:nth-child(even) {
                    margin-left: 2.4%;
                }
            }
        }
    }

    //
    //  Welcome block
    //  ---------------------------------------------

    .dashboard-welcome-toggler {
        &:extend(.abs-visually-hidden-desktop all);
    }

    .control.captcha-image {
        .captcha-img {
            margin: 0 @indent__s @indent__s 0;
        }
    }
}