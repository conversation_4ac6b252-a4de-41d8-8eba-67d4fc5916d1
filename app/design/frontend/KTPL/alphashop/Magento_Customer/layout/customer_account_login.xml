<?xml version="1.0"?>
<!--
/**
 * Theme override for Magento_Customer login page
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <!-- Create a new container for the login page content -->
        <referenceContainer name="content">
            <container name="customer.login.page.wrapper" htmlTag="div" htmlClass="customer-login-page-wrapper" before="-">
                <!-- This container will hold both the page title and login form -->
            </container>
        </referenceContainer>
        
        <!-- Move the page title into the new container -->
        <move element="page.main.title" destination="customer.login.page.wrapper" before="-"/>
        
        <!-- Move the customer login container into the new wrapper -->
        <move element="customer.login.container" destination="customer.login.page.wrapper" after="page.main.title"/>

        <!-- Move social login buttons inside the login-parent block at the bottom -->
        <move element="md.sociallogin.buttons" destination="login-parent" after="-"/>
    </body>
</page>